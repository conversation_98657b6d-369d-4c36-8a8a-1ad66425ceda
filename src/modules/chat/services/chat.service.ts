import { Injectable, Logger, Inject } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { AgentConfigService } from './agent-config.service';
import {
  UserAgentRunsQueries,
  UserMessagesQueries,
  ChatDatabaseService,
} from '../database';
import { MessageRequestDto } from '../dto/message-request.dto';
import { MessageResponseDto } from '../dto/message-response.dto';
import { UserAgentRunStatus } from '../../../shared/enums';
import { SystemAgentConfig } from '../interfaces/system-agent-config.interface';
import { ModifyResult } from '../interfaces/modify-result.interface';
import { REDIS_EVENTS, RunTriggerEvent, RunCancelEvent } from '../constants';
import { ConversationThreadService } from './conversation-thread.service';
import { AppException } from '@/common';
import { CHAT_ERROR_CODES } from '../exceptions';
import { TextContentBlockDto } from '../dto/text-content-block.dto';
import { QueryRunner } from 'typeorm';

// ✅ NEW: File metadata interfaces
interface FileMetadata {
  fileId: string;
  fileName: string;
  fileExtension: string;
  description?: string;
  fileType: 'file' | 'image';
}

interface EnhancedAttachmentContext {
  type: 'file' | 'image';
  fileId?: string;
  imageId?: string;
  fileName: string;
  fileExtension: string;
  description?: string;
}

/**
 * Service for handling chat functionality and run management
 */
@Injectable()
export class ChatService {
  private readonly logger = new Logger(ChatService.name);

  constructor(
    private readonly agentConfigService: AgentConfigService,
    private readonly userAgentRunsQueries: UserAgentRunsQueries,
    private readonly userMessagesQueries: UserMessagesQueries,
    private readonly chatDatabaseService: ChatDatabaseService,
    private readonly conversationThreadService: ConversationThreadService,
    @Inject('REDIS_CLIENT') private readonly redisClient: ClientProxy,
  ) {}

  /**
   * Process a chat message and create a run for agent processing
   * @param messageRequest Message request data
   * @param userId ID of the user sending the message
   * @param jwt JWT token for authenticated API calls
   * @returns Promise<MessageResponseDto> Response with run information
   */
  async processMessage(
    messageRequest: MessageRequestDto,
    userId: number,
    jwt: string = '',
  ): Promise<MessageResponseDto> {
    // ✅ NEW: Validate file existence and get enhanced attachment context with metadata
    let enhancedAttachmentContext: EnhancedAttachmentContext[] = [];
    if (
      messageRequest.attachmentContext &&
      messageRequest.attachmentContext.length > 0
    ) {
      enhancedAttachmentContext =
        await this.validateFileExistenceInAttachmentContext(
          messageRequest.attachmentContext as Array<{
            type: string;
            fileId?: string;
            imageId?: string;
            [key: string]: unknown;
          }>,
        );
    }

    // Validate message content before processing
    // Validate file existence in content blocks
    await this.validateFileExistenceInContentBlocks(
      messageRequest.contentBlocks,
    );

    if (messageRequest.messageId) {
      await this.validateModifyLastTextBlock(messageRequest, userId);
    }

    // Start database transaction for atomic operation
    const queryRunner = await this.chatDatabaseService.startTransaction();
    try {
      const thread = await this.conversationThreadService.findOne(
        messageRequest.threadId,
        userId,
      );
      if (!thread) {
        throw new AppException(CHAT_ERROR_CODES.INVALID_THREAD_ID);
      }

      this.logger.log(`Processing message for from user ${userId}`);

      // 3. Build complete agent configuration map for multi-agent processing
      const agentConfigMap =
        await this.agentConfigService.buildAgentConfigMap();

      let primaryAgent: SystemAgentConfig | null = null;
      let supervisorCount = 0;

      for (const agentId in agentConfigMap) {
        if (supervisorCount > 1) {
          throw new AppException(CHAT_ERROR_CODES.TOO_MANY_SUPERVISORS);
        }
        if (agentConfigMap[agentId].isSupervisor) {
          ++supervisorCount;
          primaryAgent = agentConfigMap[agentId];
        }
      }

      if (!primaryAgent) {
        throw new AppException(CHAT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      // 4. Create run payload with agent configuration, modification context, and enhanced attachment context
      const runPayload = this.buildRunPayload(
        messageRequest,
        primaryAgent,
        agentConfigMap,
        userId,
        enhancedAttachmentContext,
      );

      const threadId = messageRequest.threadId;
      if (!threadId) {
        throw new AppException(CHAT_ERROR_CODES.INVALID_THREAD_ID);
      }

      // 5. Create run in database (within transaction)
      const createRunQuery = `
        INSERT INTO user_agent_runs (payload, status, created_by, thread_id)
        VALUES ($1, $2, $3, $4)
        RETURNING id
      `;
      const runResult = await queryRunner.query(createRunQuery, [
        JSON.stringify(runPayload),
        UserAgentRunStatus.CREATED,
        userId,
        threadId,
      ]);
      const runId = runResult[0].id;

      this.logger.log(`Created run ${runId}`);

      // 6. Persist user message to database
      let userMessageId = messageRequest.messageId;
      const modifyResults: ModifyResult = {
        modifiedMessageId: '',
        deletedMessageIds: [],
        deletedMessagesCount: 0,
      };
      if (userMessageId) {
        const { modifiedMessageId, deletedMessageIds, deletedMessagesCount } =
          await this.updateMessageText(queryRunner, messageRequest, userId);
        modifyResults.modifiedMessageId = modifiedMessageId;
        modifyResults.deletedMessageIds = deletedMessageIds;
        modifyResults.deletedMessagesCount = deletedMessagesCount;
      } else {
        const { messageId } = await this.insertMessageText(
          queryRunner,
          messageRequest,
          userId,
        );
        userMessageId = messageId;
      }

      // 9. Publish run trigger event to Redis for worker consumption
      // 🔍 DEBUG: Log trigger event threadId details
      this.logger.debug(`🔍 BACKEND TRIGGER - ThreadId details:`, {
        runId,
        threadId,
        messageRequestThreadId: messageRequest.threadId,
        threadIdType: typeof threadId,
        threadIdLength: threadId?.length,
        messageRequestKeys: Object.keys(messageRequest),
      });

      const runTriggerEvent: RunTriggerEvent = {
        eventType: REDIS_EVENTS.RUN_TRIGGER,
        runId,
        threadId, // ✅ CLEAN: Use same threadId as database
        agentId: primaryAgent.id,
        userId,
        jwt, // JWT token for authenticated API calls
        timestamp: Date.now(),
        priority: 'medium',
        publishedAt: Date.now(),
      };

      // Use emit for fire-and-forget pub/sub pattern
      this.redisClient.emit(REDIS_EVENTS.RUN_TRIGGER, runTriggerEvent);
      this.logger.log(
        `Published run trigger event for run ${runId} (thread ${threadId})`,
      );

      // 🔍 DEBUG: Log published trigger event details
      this.logger.debug(`🔍 BACKEND PUBLISHED trigger event:`, {
        eventType: REDIS_EVENTS.RUN_TRIGGER,
        publishedThreadId: threadId,
        publishedRunId: runId,
        fullEvent: runTriggerEvent,
      });

      // 10. Commit transaction - all database operations successful
      await queryRunner.commitTransaction();
      this.logger.log(`Transaction committed for run ${runId}`);

      // 11. Return response with modification details if applicable
      const responseData: any = {
        messageId: userMessageId,
        runId,
        agentName: primaryAgent.name,
        status: UserAgentRunStatus.CREATED,
        createdAt: Date.now(),
      };

      // Add modification details if modifications were made
      if (modifyResults) {
        responseData.modificationDetails = {
          modifiedMessageId: messageRequest.messageId,
          deletedMessageIds: modifyResults.deletedMessageIds,
          deletedMessagesCount: modifyResults.deletedMessagesCount,
        };
      }

      return new MessageResponseDto(responseData);
    } catch (error) {
      // Rollback transaction on any error
      await queryRunner.rollbackTransaction();
      this.logger.error(
        `Transaction rolled back: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        CHAT_ERROR_CODES.MESSAGE_PROCESSING_FAILED,
        `Failed to process message: ${error.message}`,
      );
    } finally {
      // Always release the query runner
      await queryRunner.release();
    }
  }

  /**
   * Validate modify_last_text content block
   * @param messageRequest Message Request for editing
   * @param userId User ID for validation
   */
  private async validateModifyLastTextBlock(
    messageRequest: MessageRequestDto,
    userId: number,
  ): Promise<void> {
    // 1. Check if the target message exists and belongs to the user
    const targetMessageQuery = `SELECT message_id, role, content, created_by
                                FROM user_messages
                                WHERE created_by = $1 AND role = $2
                                ORDER BY timestamp DESC
                                LIMIT 1`;

    const messageResult = await this.chatDatabaseService.query(
      targetMessageQuery,
      [userId, 'user'],
    );

    if (!messageResult || messageResult.length === 0) {
      throw new AppException(
        CHAT_ERROR_CODES.MESSAGE_NOT_FOUND,
        `User have not submitted any messages`,
      );
    }

    const targetMessage = messageResult[0];
    if (targetMessage.message_id !== messageRequest.messageId) {
      throw new AppException(
        CHAT_ERROR_CODES.MESSAGE_CASCADE_DELETE_FAILED,
        'Not last message'
      )
    }
  }

  private async insertMessageText(
    queryRunner: QueryRunner,
    messageRequest: MessageRequestDto,
    userId: number,
  ): Promise<{ messageId: string }> {
    // Only save user message if it's not a modify-only request
    const createMessageQuery = `
          INSERT INTO user_messages (thread_id, role, content, timestamp, created_by)
          VALUES ($1, $2, $3, $4, $5)
          RETURNING message_id
        `;

    // ✅ UPDATED: Remove threadId from message content to eliminate redundancy
    const messageContentWithoutThreadId = {
      ...messageRequest,
      threadId: undefined, // Remove threadId - it's already stored as separate column
    };

    const messageResult = await queryRunner.query(createMessageQuery, [
      messageRequest.threadId,
      'user',
      JSON.stringify(messageContentWithoutThreadId), // ✅ Save content without redundant threadId
      Date.now(),
      userId,
    ]);

    this.logger.log(
      `Persisted user message ${messageResult[0].message_id} for thread ${messageRequest.threadId} (threadId removed from content)`,
    );
    return { messageId: messageResult[0].message_id };
  }

  /**
   * Update message text content and delete all subsequent messages (cascade delete)
   * @param modifyBlock Modify text content block
   * @param userId User ID for processing
   * @returns Promise<ModifyResult> Result containing modified message ID and deleted message IDs
   */
  private async updateMessageText(
    queryRunner: QueryRunner,
    messageRequest: MessageRequestDto,
    userId: number,
  ): Promise<ModifyResult> {
    // Start transaction for atomic operation

    // 1. Get the target message details (thread_id, timestamp, content)
    const getMessageQuery = `
        SELECT message_id, thread_id, timestamp, content
        FROM user_messages
        WHERE message_id = $1 AND created_by = $2
      `;

    const messageResult = await queryRunner.query(getMessageQuery, [
      messageRequest.messageId,
      userId,
    ]);

    if (!messageResult || messageResult.length === 0) {
      throw new AppException(
        CHAT_ERROR_CODES.MESSAGE_NOT_FOUND,
        `Message ${messageRequest.messageId} not found for update`,
      );
    }

    const targetMessage = messageResult[0];
    let { thread_id: threadId, timestamp: targetTimestamp } = targetMessage;

    // 2. Delete all messages after the target message (using timestamp + message_id for precision)
    const deleteSubsequentQuery = `
        DELETE FROM user_messages
        WHERE thread_id = $1
        AND (
          timestamp > $2
          OR (timestamp = $2 AND message_id != $3)
        )
        RETURNING message_id, role, timestamp
      `;

    const deletedMessages = await queryRunner.query(deleteSubsequentQuery, [
      threadId,
      targetTimestamp,
      messageRequest.messageId, // Exclude target message itself
    ]);

    this.logger.log(
      `Deleted the following messages ${JSON.stringify(deletedMessages, null, 2)}`,
    );

    // 4. Create updated message content
    const updatedContent = {
      ...messageRequest,
      threadId: undefined,
      messageId: undefined,
    };

    // 5. Update the target message in database
    const updateMessageQuery = `
        UPDATE user_messages
        SET content = $1, updated_at = $2
        WHERE message_id = $3 AND created_by = $4
      `;

    await queryRunner.query(updateMessageQuery, [
      JSON.stringify(updatedContent),
      Date.now(),
      messageRequest.messageId,
      userId,
    ]);

    this.logger.log(
      `Successfully modified message ${messageRequest.messageId} and deleted ${deletedMessages.length} subsequent messages`,
    );

    // 7. Return modification result
    return {
      modifiedMessageId: messageRequest.messageId as string,
      deletedMessageIds:
        deletedMessages[0]
          .filter((msg) => !!msg)
          .map((msg: any) => msg?.message_id) || [],
      deletedMessagesCount: deletedMessages[0].length,
    };
  }

  /**
   * Validate file existence in content blocks
   * @param contentBlocks Array of content blocks to validate
   */
  private async validateFileExistenceInContentBlocks(
    contentBlocks: any[],
  ): Promise<void> {
    for (const block of contentBlocks) {
      if (block.type === 'file' && block.fileId) {
        await this.validateFileExists(block.fileId, 'file');
      } else if (block.type === 'image' && block.fileId) {
        await this.validateFileExists(block.fileId, 'image');
      }
    }
  }

  /**
   * Validate file existence in attachment context and return enhanced metadata
   * @param attachmentContext Array of attachment context to validate
   * @returns Array of enhanced attachment context with file metadata
   */
  private async validateFileExistenceInAttachmentContext(
    attachmentContext: Array<{
      type: string;
      fileId?: string;
      imageId?: string;
      [key: string]: unknown;
    }>,
  ): Promise<EnhancedAttachmentContext[]> {
    const enhancedAttachmentContext: EnhancedAttachmentContext[] = [];

    for (const context of attachmentContext) {
      if (context.type === 'file' && context.fileId) {
        const fileMetadata = await this.validateFileExists(
          context.fileId,
          'file',
        );
        enhancedAttachmentContext.push({
          type: 'file' as const,
          fileId: context.fileId,
          fileName: fileMetadata.fileName,
          fileExtension: fileMetadata.fileExtension,
          description: fileMetadata.description,
        });
      } else if (context.type === 'image' && context.imageId) {
        const imageMetadata = await this.validateFileExists(
          context.imageId,
          'image',
        );
        enhancedAttachmentContext.push({
          type: 'image' as const,
          imageId: context.imageId,
          fileName: imageMetadata.fileName,
          fileExtension: imageMetadata.fileExtension,
          description: imageMetadata.description,
        });
      } else {
        // Skip unknown types since we can't enhance them
        this.logger.warn(`Unknown attachment context type: ${context.type}`, {
          context,
        });
      }
    }

    return enhancedAttachmentContext;
  }

  /**
   * Validate if a file exists and return file metadata (enhanced stub implementation)
   * @param fileId UUID of the file to validate
   * @param fileType Type of file ('file' or 'image')
   * @returns File metadata object with name, extension, and description
   */
  private async validateFileExists(
    fileId: string,
    fileType: 'file' | 'image',
  ): Promise<FileMetadata> {
    this.logger.debug(`[STUB] Validating ${fileType} existence: ${fileId}`);

    // TODO: Replace with actual file validation when storage is ready
    // Example implementation:
    // const fileMetadata = await this.fileValidationService.getFileMetadata(fileId);
    // if (!fileMetadata) {
    //   throw new AppException(
    //     fileType === 'image' ? CHAT_ERROR_CODES.IMAGE_NOT_FOUND : CHAT_ERROR_CODES.FILE_NOT_FOUND,
    //     `${fileType} with ID ${fileId} does not exist or is not accessible`
    //   );
    // }
    // return fileMetadata;

    // ✅ ENHANCED STUB: Return mock file metadata
    const mockMetadata = this.generateMockFileMetadata(fileId, fileType);
    this.logger.debug(
      `[STUB] File validation passed for ${fileType}: ${fileId}`,
      {
        fileName: mockMetadata.fileName,
        fileExtension: mockMetadata.fileExtension,
        description: mockMetadata.description,
      },
    );

    return mockMetadata;
  }

  /**
   * Generate mock file metadata for testing (stub implementation)
   * @param fileId File ID
   * @param fileType File type
   * @returns Mock file metadata
   */
  private generateMockFileMetadata(
    fileId: string,
    fileType: 'file' | 'image',
  ): FileMetadata {
    // Generate realistic mock data based on file type
    if (fileType === 'image') {
      const imageTypes = [
        {
          ext: 'jpg',
          name: 'Screenshot_2024',
          desc: 'Screenshot from mobile app',
        },
        {
          ext: 'png',
          name: 'Design_mockup',
          desc: 'UI design mockup for new feature',
        },
        {
          ext: 'gif',
          name: 'Animation_demo',
          desc: 'Animated demonstration of workflow',
        },
        { ext: 'webp', name: 'Profile_photo', desc: 'User profile picture' },
      ];
      const randomImage =
        imageTypes[Math.floor(Math.random() * imageTypes.length)];

      return {
        fileId,
        fileName: `${randomImage.name}_${fileId.substring(0, 8)}.${randomImage.ext}`,
        fileExtension: randomImage.ext,
        description: randomImage.desc,
        fileType: 'image',
      };
    } else {
      const fileTypes = [
        {
          ext: 'pdf',
          name: 'Document_report',
          desc: 'Monthly business report in PDF format',
        },
        {
          ext: 'docx',
          name: 'Meeting_notes',
          desc: 'Meeting minutes and action items',
        },
        {
          ext: 'xlsx',
          name: 'Data_analysis',
          desc: 'Spreadsheet with quarterly data analysis',
        },
        {
          ext: 'txt',
          name: 'Config_file',
          desc: 'Configuration file for system setup',
        },
        {
          ext: 'json',
          name: 'API_response',
          desc: 'JSON data from API endpoint',
        },
      ];
      const randomFile =
        fileTypes[Math.floor(Math.random() * fileTypes.length)];

      return {
        fileId,
        fileName: `${randomFile.name}_${fileId.substring(0, 8)}.${randomFile.ext}`,
        fileExtension: randomFile.ext,
        description: randomFile.desc,
        fileType: 'file',
      };
    }
  }

  /**
   * Build run payload for agent processing
   * @param messageRequest Message request data
   * @param agentConfig Primary agent configuration
   * @param agentConfigMap Complete agent configuration map
   * @param userId User ID for processing
   * @param modifyResults Optional modification results for context
   * @param enhancedAttachmentContext Enhanced attachment context with file metadata
   * @returns Run payload object
   */
  private buildRunPayload(
    messageRequest: MessageRequestDto,
    agentConfig: SystemAgentConfig,
    agentConfigMap: { [agentId: string]: SystemAgentConfig },
    userId: number,
    enhancedAttachmentContext?: EnhancedAttachmentContext[],
  ): any {
    // Build the run payload - pass through request with enhanced attachment context
    const payload = {
      message: {
        ...messageRequest,
        // ✅ NEW: Replace original attachment context with enhanced version containing file metadata
        attachmentContext:
          enhancedAttachmentContext && enhancedAttachmentContext.length > 0
            ? enhancedAttachmentContext
            : messageRequest.attachmentContext || [],
      },

      // Primary agent ID (full config available in agentConfigMap)
      primaryAgentId: agentConfig.id,

      // Complete agent configuration map for multi-agent processing
      agentConfigMap,

      // Processing metadata
      metadata: {
        userId, // Essential: actual user ID for processing
        requestId: `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        version: '1.0',
      },

      // Processing configuration
      processing: {
        maxRetries: 3,
        timeoutMs: 300000, // 5 minutes
        enableMultiAgent: Object.keys(agentConfigMap).length > 1,
        alwaysApproveToolCall: messageRequest.alwaysApproveToolCall || false,
      },
    };

    this.logger.debug(`Built run payload for agent ${agentConfig.id}`, {
      agentId: agentConfig.id,
      contentBlockCount: messageRequest.contentBlocks.length,
      attachmentCount: messageRequest.attachmentContext?.length || 0,
      multiAgentEnabled: payload.processing.enableMultiAgent,
      agentCount: Object.keys(agentConfigMap).length,
      alwaysApproveToolCall: payload.processing.alwaysApproveToolCall,
    });

    return payload;
  }

  /**
   * Cancel a run and notify worker
   * @param runId Run ID to cancel
   * @param reason Cancellation reason
   * @returns Promise<boolean> True if cancelled successfully
   */
  async cancelRun(
    runId: string,
    reason: string = 'User requested cancellation',
  ): Promise<boolean> {
    try {
      this.logger.log(`Cancelling run ${runId}: ${reason}`);

      // 1. Get run data to extract threadId
      const runData = await this.userAgentRunsQueries.getRunById(runId);
      if (!runData) {
        this.logger.error(`Run not found for cancellation: ${runId}`);
        return false;
      }

      // ✅ CLEAN: Get threadId from database (single source of truth)
      const threadId = runData.thread_id;
      if (!threadId) {
        this.logger.error(`No threadId found for run ${runId}`);
        return false;
      }

      // 🔍 DEBUG: Log threadId extraction details
      this.logger.debug(`🔍 BACKEND CANCEL - ThreadId extraction:`, {
        runId,
        reason,
        extractedThreadId: threadId,
        threadIdType: typeof threadId,
        threadIdLength: threadId?.length,
        payloadKeys: Object.keys(runData.payload || {}),
        messageKeys: Object.keys(runData.payload?.message || {}),
      });

      // 3. Publish run cancel event to Redis using correct threadId
      try {
        const runCancelEvent: RunCancelEvent = {
          eventType: REDIS_EVENTS.RUN_CANCEL,
          threadId, // Use actual threadId from frontend (LangGraph thread)
          runId,
          reason,
          timestamp: Date.now(),
          publishedAt: Date.now(),
        };

        // Use emit for fire-and-forget pub/sub pattern
        this.redisClient.emit(REDIS_EVENTS.RUN_CANCEL, runCancelEvent);
        this.logger.log(
          `Published run cancel event for thread ${threadId} (run ${runId})`,
        );

        // 🔍 DEBUG: Log published event details
        this.logger.debug(`🔍 BACKEND PUBLISHED cancel event:`, {
          eventType: REDIS_EVENTS.RUN_CANCEL,
          publishedThreadId: threadId,
          publishedRunId: runId,
          publishedReason: reason,
          fullEvent: runCancelEvent,
        });
      } catch (error) {
        this.logger.warn(
          `Failed to publish run cancel event for run ${runId}: ${error.message}`,
        );
        // Run is still cancelled in database even if Redis publish fails
      }

      this.logger.log(
        `Successfully cancelled run ${runId} (thread ${threadId})`,
      );
      return true;
    } catch (error) {
      this.logger.error(
        `Failed to cancel run ${runId}: ${error.message}`,
        error.stack,
      );
      return false;
    }
  }
}
